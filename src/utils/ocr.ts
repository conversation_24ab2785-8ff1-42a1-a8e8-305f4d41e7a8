import { createWorker } from 'tesseract.js';
import fs from 'fs';
import path from 'path';
import pdfParse from 'pdf-parse';
import pdf from 'pdf-poppler';

// Configure Tesseract.js for serverless environment (Vercel)
// In serverless environments, we need to use the Node.js API without external worker scripts
const TESSERACT_CONFIG = {
  // For serverless environments, we don't specify workerPath to use the default Node.js implementation
  langPath: 'https://tessdata.projectnaptha.com/4.0.0',
  // Don't specify corePath and workerPath to use the built-in Node.js implementation
};

// File type detection
export enum FileType {
  IMAGE = 'image',
  PDF = 'pdf',
  UNSUPPORTED = 'unsupported'
}

// Configuration for PDF processing
const PDF_CONFIG = {
  maxPages: 10, // Maximum pages to process
  maxFileSize: 50 * 1024 * 1024, // 50MB max
  imageQuality: 300, // DPI for PDF to image conversion
  timeout: 60000, // 60 seconds timeout
};

export interface ExtractedDocumentData {
  surname?: string;
  name?: string;
  dateOfBirth?: string;
  citizenship?: string;
  passportNumber?: string;
  passportIssueDate?: string;
  passportExpiryDate?: string;
  iin?: string;
  idNumber?: string;
  gender?: string;
  nationality?: string;
  birthPlace?: string;
  rawText?: string; // Store the raw text for debugging
}

// File type detection function
export const detectFileType = async (filePath: string): Promise<FileType> => {
  try {
    console.log('Detecting file type for:', filePath);

    if (!fs.existsSync(filePath)) {
      throw new Error('File does not exist');
    }

    // Read first few bytes to detect file signature
    const fd = fs.openSync(filePath, 'r');
    const buffer = Buffer.alloc(10);
    fs.readSync(fd, buffer, 0, 10, 0);
    fs.closeSync(fd);

    // Check PDF signature
    if (buffer.toString('ascii', 0, 4) === '%PDF') {
      console.log('Detected file type: PDF');
      return FileType.PDF;
    }

    // Check image signatures
    const signatures = {
      jpeg: [0xFF, 0xD8, 0xFF],
      png: [0x89, 0x50, 0x4E, 0x47],
      bmp: [0x42, 0x4D],
      gif: [0x47, 0x49, 0x46],
      webp: [0x52, 0x49, 0x46, 0x46] // RIFF (WebP container)
    };

    for (const [type, signature] of Object.entries(signatures)) {
      if (signature.every((byte, index) => buffer[index] === byte)) {
        console.log(`Detected file type: ${type.toUpperCase()} image`);
        return FileType.IMAGE;
      }
    }

    // Fallback: check file extension
    const ext = path.extname(filePath).toLowerCase();
    if (['.pdf'].includes(ext)) {
      console.log('Detected file type by extension: PDF');
      return FileType.PDF;
    }

    if (['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp', '.tiff', '.tif'].includes(ext)) {
      console.log('Detected file type by extension: IMAGE');
      return FileType.IMAGE;
    }

    console.log('File type not supported');
    return FileType.UNSUPPORTED;
  } catch (error) {
    console.error('Error detecting file type:', error);
    return FileType.UNSUPPORTED;
  }
};

// Extract text directly from PDF (if it has text layer)
export const extractTextFromPdf = async (pdfPath: string): Promise<string> => {
  try {
    console.log('Attempting to extract text directly from PDF...');
    const dataBuffer = fs.readFileSync(pdfPath);
    const data = await pdfParse(dataBuffer);

    if (data.text && data.text.trim().length > 0) {
      console.log('Successfully extracted text from PDF:', data.text.length, 'characters');
      return data.text;
    }

    console.log('No text found in PDF, will need OCR');
    return '';
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return '';
  }
};

// Convert PDF to images for OCR processing
export const convertPdfToImages = async (pdfPath: string): Promise<string[]> => {
  try {
    console.log('Converting PDF to images for OCR...');

    // Create temporary directory for images
    const tempDir = path.join(process.env.VERCEL ? '/tmp' : path.join(process.cwd(), 'tmp'), `pdf_${Date.now()}`);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const options = {
      format: 'jpeg' as const,
      out_dir: tempDir,
      out_prefix: 'page',
      page: null, // Convert all pages
      quality: PDF_CONFIG.imageQuality
    };

    console.log('PDF conversion options:', options);

    // Convert PDF to images
    const pages = await pdf.convert(pdfPath, options);
    console.log('PDF converted to', pages.length, 'images');

    // Get list of generated image files
    const imageFiles = fs.readdirSync(tempDir)
      .filter(file => file.startsWith('page') && file.endsWith('.jpg'))
      .map(file => path.join(tempDir, file))
      .sort(); // Sort to maintain page order

    console.log('Generated image files:', imageFiles);
    return imageFiles;
  } catch (error) {
    console.error('Error converting PDF to images:', error);
    throw new Error(`Failed to convert PDF to images: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Helper function to create worker for serverless environment
const createWorkerForServerless = async (languages: string) => {
  try {
    console.log(`Creating Tesseract worker for serverless environment with languages: ${languages}`);

    // For serverless environments, use the simplified API without external worker paths
    // This uses the built-in Node.js implementation
    const worker = await createWorker(languages, undefined, {
      langPath: TESSERACT_CONFIG.langPath,
      // Don't specify workerPath or corePath to use built-in Node.js implementation
    });

    console.log('Tesseract worker created successfully for serverless environment');
    return worker;
  } catch (error) {
    console.error('Failed to create serverless worker:', error);

    // Try with just English if multi-language fails
    if (languages !== 'eng') {
      console.log('Retrying with English only...');
      return await createWorkerForServerless('eng');
    }

    // If English also fails, try without any language path
    console.log('Trying without language path...');
    try {
      const worker = await createWorker('eng');
      console.log('Worker created without language path');
      return worker;
    } catch (finalError) {
      console.error('All worker creation attempts failed:', finalError);
      throw new Error(`Failed to create OCR worker: ${finalError instanceof Error ? finalError.message : 'Unknown error'}`);
    }
  }
};

export const extractTextFromImage = async (
  fileOrPath: File | string,
  _fileType?: string // Add underscore to indicate intentionally unused parameter
): Promise<string> => {
  let worker = null;

  try {
    console.log('Starting OCR extraction...');

    if (typeof fileOrPath === 'string') {
      console.log('File path provided for OCR:', fileOrPath);
    } else {
      console.log('File info:', {
        name: fileOrPath.name,
        type: fileOrPath.type,
        size: `${(fileOrPath.size / (1024 * 1024)).toFixed(2)} MB`
      });
    }

    // Try to use multiple languages for better recognition in serverless environment
    console.log('Creating OCR worker for serverless environment...');

    try {
      worker = await createWorkerForServerless('eng+rus+kaz');
      console.log('Multi-language worker created successfully');
    } catch (workerError) {
      console.error('Failed to create multi-language worker, trying English only:', workerError);
      try {
        worker = await createWorkerForServerless('eng');
        console.log('English-only worker created successfully');
      } catch (englishWorkerError) {
        console.error('Failed to create English worker:', englishWorkerError);
        throw new Error(`Failed to create OCR worker: ${englishWorkerError instanceof Error ? englishWorkerError.message : 'Unknown error'}`);
      }
    }

    try {
      console.log('Starting recognition...');
      const { data: { text } } = await worker.recognize(fileOrPath);
      console.log('Recognition completed, text length:', text.length);
      return text;
    } catch (recognitionError) {
      console.error('OCR recognition error:', recognitionError);
      throw recognitionError;
    }
  } catch (error) {
    console.error('OCR Error:', error);
    throw new Error(`Failed to extract text from image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Ensure worker is always terminated
    if (worker) {
      try {
        await worker.terminate();
        console.log('OCR worker terminated successfully');
      } catch (terminateError) {
        console.error('Error terminating OCR worker:', terminateError);
      }
    }
  }
};

export const parsePassportData = (text: string): ExtractedDocumentData => {
  console.log('Parsing passport data from text...');
  
  const data: ExtractedDocumentData = {
    rawText: text // Store raw text for debugging
  };
  
  // Clean and normalize text to improve matching
  const normalizedText = text
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s.,:<>()\/\-]/g, ' ')
    .trim();
  
  console.log('Normalized text length:', normalizedText.length);
  
  // Extract passport number - pattern like: "N12345678" or similar formats
  const passportNumberPatterns = [
    /\b[A-Z]\d{8}\b/,                             // Standard format N12345678
    /Passport No\.?:?\s*([A-Z0-9]{7,9})/i,        // With label
    /№\s*([A-Z0-9]{7,9})/i,                       // With № symbol
    /Паспорт\s*№?\s*([A-Z0-9]{7,9})/i,            // Russian/Kazakh label
    /номер\s*(?:паспорта)?:?\s*([A-Z0-9]{7,9})/i  // Another Russian variant
  ];
  
  for (const pattern of passportNumberPatterns) {
    const match = normalizedText.match(pattern);
    if (match && (match[1] || match[0])) {
      data.passportNumber = match[1] || match[0];
      console.log('Found passport number:', data.passportNumber);
      break;
    }
  }
  
  // Extract IIN (Individual Identification Number) - 12 digits
  const iinMatch = normalizedText.match(/\b\d{12}\b/);
  if (iinMatch) {
    data.iin = iinMatch[0];
    console.log('Found IIN:', data.iin);
  }
  
  // Extract name patterns with various labels in different languages
  const namePatterns = [
    /(?:Given names|Name|Имя|Аты|Имена)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:first name|given name)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:имя|имена)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of namePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.name = match[1].trim();
      console.log('Found name:', data.name);
      break;
    }
  }
  
  // If we still don't have a name, try a more generic approach
  if (!data.name) {
    // Look for capitalized words near name-related terms
    const genericNameMatches = normalizedText.match(/(?:name|имя|first|given|имена)[:\s]+([A-ZА-Я][a-zа-я]+)/i);
    if (genericNameMatches && genericNameMatches[1]) {
      data.name = genericNameMatches[1].trim();
      console.log('Found name using generic approach:', data.name);
    }
  }
  
  // Extract surname patterns
  const surnamePatterns = [
    /(?:Surname|Фамилия|Тегі)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:last name|family name)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:фамилия)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of surnamePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.surname = match[1].trim();
      console.log('Found surname:', data.surname);
      break;
    }
  }
  
  // If we still don't have a surname, try a more generic approach
  if (!data.surname && data.name) {
    // Look for capitalized words near surname-related terms
    const genericSurnameMatches = normalizedText.match(/(?:surname|family|фамилия|last)[:\s]+([A-ZА-Я][a-zа-я]+)/i);
    if (genericSurnameMatches && genericSurnameMatches[1]) {
      data.surname = genericSurnameMatches[1].trim();
      console.log('Found surname using generic approach:', data.surname);
    }
  }
  
  // Extract gender
  const genderMatch = normalizedText.match(/(?:Gender|Sex|Пол|Жынысы)[:\s]+([MFmfМЖмж]|Male|Female|Муж|Жен|мужской|женский)/i);
  if (genderMatch && genderMatch[1]) {
    const genderValue = genderMatch[1].trim().toLowerCase();
    if (['m', 'male', 'м', 'муж', 'мужской'].includes(genderValue)) {
      data.gender = 'M';
      console.log('Found gender: Male');
    } else if (['f', 'female', 'ж', 'жен', 'женский'].includes(genderValue)) {
      data.gender = 'F';
      console.log('Found gender: Female');
    }
  }
  
  // Extract nationality
  const nationalityMatch = normalizedText.match(/(?:Nationality|Национальность|Ұлты)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i);
  if (nationalityMatch && nationalityMatch[1]) {
    data.nationality = nationalityMatch[1].trim();
    console.log('Found nationality:', data.nationality);
  }
  
  // Extract dates (birth, issue, expiry)
  // Format: DD.MM.YYYY or YYYY-MM-DD or DD MMM YYYY
  const datePatterns = [
    /\b(\d{2}[./-]\d{2}[./-]\d{4})\b/g,  // DD.MM.YYYY or DD/MM/YYYY or DD-MM-YYYY
    /\b(\d{4}[./-]\d{2}[./-]\d{2})\b/g,  // YYYY.MM.DD or YYYY/MM/DD or YYYY-MM-DD
    /\b(\d{2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4})\b/gi  // DD MMM YYYY
  ];
  
  let dates: string[] = [];
  for (const pattern of datePatterns) {
    const matches = [...normalizedText.matchAll(pattern)].map(match => match[0]);
    dates = [...dates, ...matches];
  }
  
  console.log('Found dates:', dates);
  
  // Assign dates based on context clues in surrounding text
  for (let i = 0; i < dates.length; i++) {
    const currentDate = dates[i];
    // Look for context 50 characters before and after the date
    const dateContext = normalizedText.substring(
      Math.max(0, normalizedText.indexOf(currentDate) - 50), 
      Math.min(normalizedText.length, normalizedText.indexOf(currentDate) + currentDate.length + 50)
    );
    
    if (dateContext.match(/birth|рожд|date of birth|дата рождения|туған|туылған|born/i)) {
      data.dateOfBirth = currentDate;
      console.log('Found date of birth:', currentDate);
    } else if (dateContext.match(/issue|выда|date of issue|дата выдачи|берілген|берілді/i)) {
      data.passportIssueDate = currentDate;
      console.log('Found issue date:', currentDate);
    } else if (dateContext.match(/expir|действ|valid until|годен до|дейін жарамды|expiry|expire/i)) {
      data.passportExpiryDate = currentDate;
      console.log('Found expiry date:', currentDate);
    }
  }
  
  // If we have multiple dates but couldn't associate them by context, 
  // make an educated guess based on chronological order
  if (dates.length > 0) {
    // Try to convert dates to timestamps for comparison
    try {
      const sortedDates = [...dates].sort((a, b) => {
        // Convert to a common format (YYYY-MM-DD)
        const formatDate = (dateStr: string): string => {
          // DD.MM.YYYY or DD/MM/YYYY or DD-MM-YYYY
          if (/^\d{2}[./-]\d{2}[./-]\d{4}$/.test(dateStr)) {
            const [day, month, year] = dateStr.split(/[./-]/);
            return `${year}-${month}-${day}`;
          }
          // YYYY.MM.DD or YYYY/MM/DD or YYYY-MM-DD
          if (/^\d{4}[./-]\d{2}[./-]\d{2}$/.test(dateStr)) {
            return dateStr.replace(/[./]/g, '-');
          }
          // DD MMM YYYY
          if (/^\d{2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4}$/i.test(dateStr)) {
            const [day, month, year] = dateStr.split(/\s+/);
            const monthMap: {[key: string]: string} = {
              'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 'may': '05', 'jun': '06',
              'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
            };
            const monthNum = monthMap[month.toLowerCase().substring(0, 3)];
            return `${year}-${monthNum}-${day}`;
          }
          return dateStr;
        };
        
        return new Date(formatDate(a)).getTime() - new Date(formatDate(b)).getTime();
      });
      
      // If we have multiple dates but couldn't assign them by context
      if (sortedDates.length >= 3) {
        if (!data.dateOfBirth) {
          data.dateOfBirth = sortedDates[0]; // Earliest date is likely birth date
          console.log('Assigned birth date by chronology:', sortedDates[0]);
        }
        if (!data.passportIssueDate) {
          data.passportIssueDate = sortedDates[1]; // Middle date is likely issue date
          console.log('Assigned issue date by chronology:', sortedDates[1]);
        }
        if (!data.passportExpiryDate) {
          data.passportExpiryDate = sortedDates[sortedDates.length - 1]; // Latest date is likely expiry
          console.log('Assigned expiry date by chronology:', sortedDates[sortedDates.length - 1]);
        }
      } else if (sortedDates.length === 2) {
        // If we have only two dates, make a reasonable guess
        if (!data.dateOfBirth) {
          data.dateOfBirth = sortedDates[0]; // Earlier date is likely birth date
          console.log('Assigned birth date by chronology (2 dates):', sortedDates[0]);
        }
        if (!data.passportIssueDate) {
          data.passportIssueDate = sortedDates[1]; // Later date is likely issue date
          console.log('Assigned issue date by chronology (2 dates):', sortedDates[1]);
        }
      }
    } catch (error) {
      console.error('Error sorting dates:', error);
    }
  }
  
  // Extract citizenship
  const citizenshipPatterns = [
    /(?:Citizenship|Nationality|Гражданство|Азаматтығы)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:Citizen of|Country)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:гражданство)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of citizenshipPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.citizenship = match[1].trim();
      console.log('Found citizenship:', data.citizenship);
      break;
    }
  }
  
  // Extract birth place
  const birthPlacePatterns = [
    /(?:Place of birth|Birth place|Место рождения|Туған жері)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё\s]+?)(?:\b\d|\n|,)/i,
    /(?:Born in|Born at)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё\s]+?)(?:\b\d|\n|,)/i,
    /(?:место рождения)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё\s]+?)(?:\b\d|\n|,)/i
  ];
  
  for (const pattern of birthPlacePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.birthPlace = match[1].trim();
      console.log('Found birth place:', data.birthPlace);
      break;
    }
  }
  
  // Extract ID card number (usually 9 digits)
  const idNumberPatterns = [
    /(?:ID Number|№ удостоверения|ID card no|Жеке куәлік №)[:\s]*(\d{9})/i,
    /(?:ID|Identity card)[:\s]*(\d{9})/i,
    /(?:удостоверение личности|личное удостоверение)[:\s]*(?:№)?(\d{9})/i
  ];
  
  for (const pattern of idNumberPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.idNumber = match[1];
      console.log('Found ID number:', data.idNumber);
      break;
    }
  }
  
  return data;
};

// Universal document processing function
export const processDocument = async (filePath: string): Promise<ExtractedDocumentData> => {
  try {
    console.log('=== STARTING DOCUMENT PROCESSING ===');
    console.log('File path:', filePath);

    // Step 1: Detect file type
    const fileType = await detectFileType(filePath);
    console.log('Detected file type:', fileType);

    if (fileType === FileType.UNSUPPORTED) {
      throw new Error('Unsupported file type. Please upload a PDF or image file (JPG, PNG, etc.)');
    }

    let extractedText = '';
    let tempFiles: string[] = [];

    try {
      if (fileType === FileType.PDF) {
        console.log('=== PROCESSING PDF DOCUMENT ===');

        // Step 2a: Try to extract text directly from PDF
        const directText = await extractTextFromPdf(filePath);

        if (directText && directText.trim().length > 50) {
          console.log('Successfully extracted text directly from PDF');
          extractedText = directText;
        } else {
          console.log('PDF has no text layer or insufficient text, converting to images for OCR...');

          // Step 2b: Convert PDF to images and process with OCR
          const imageFiles = await convertPdfToImages(filePath);
          tempFiles = imageFiles;

          console.log('Processing', imageFiles.length, 'pages with OCR...');

          const ocrResults: string[] = [];
          for (let i = 0; i < Math.min(imageFiles.length, PDF_CONFIG.maxPages); i++) {
            const imagePath = imageFiles[i];
            console.log(`Processing page ${i + 1}/${imageFiles.length}: ${imagePath}`);

            try {
              const pageText = await extractTextFromImage(imagePath);
              if (pageText && pageText.trim()) {
                ocrResults.push(pageText);
                console.log(`Page ${i + 1} OCR completed, extracted ${pageText.length} characters`);
              }
            } catch (pageError) {
              console.error(`Error processing page ${i + 1}:`, pageError);
              // Continue with other pages
            }
          }

          extractedText = ocrResults.join('\n\n--- PAGE BREAK ---\n\n');
          console.log('Combined OCR text from', ocrResults.length, 'pages');
        }
      } else if (fileType === FileType.IMAGE) {
        console.log('=== PROCESSING IMAGE DOCUMENT ===');

        // Step 2c: Process image directly with OCR
        extractedText = await extractTextFromImage(filePath);
      }

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('No text could be extracted from the document');
      }

      console.log('=== TEXT EXTRACTION COMPLETED ===');
      console.log('Total extracted text length:', extractedText.length);

      // Step 3: Parse the extracted text for passport data
      console.log('=== PARSING PASSPORT DATA ===');
      const parsedData = parsePassportData(extractedText);

      console.log('=== DOCUMENT PROCESSING COMPLETED ===');
      console.log('Extracted data summary:', {
        hasName: !!parsedData.name,
        hasSurname: !!parsedData.surname,
        hasPassportNumber: !!parsedData.passportNumber,
        hasDateOfBirth: !!parsedData.dateOfBirth,
        hasGender: !!parsedData.gender,
        hasNationality: !!parsedData.nationality
      });

      return parsedData;

    } finally {
      // Clean up temporary files
      if (tempFiles.length > 0) {
        console.log('Cleaning up', tempFiles.length, 'temporary files...');
        for (const tempFile of tempFiles) {
          try {
            if (fs.existsSync(tempFile)) {
              fs.unlinkSync(tempFile);
            }
          } catch (cleanupError) {
            console.warn('Failed to clean up temp file:', tempFile, cleanupError);
          }
        }

        // Also clean up the temporary directory
        try {
          const tempDir = path.dirname(tempFiles[0]);
          if (fs.existsSync(tempDir) && tempDir.includes('pdf_')) {
            fs.rmSync(tempDir, { recursive: true, force: true });
            console.log('Cleaned up temporary directory:', tempDir);
          }
        } catch (dirCleanupError) {
          console.warn('Failed to clean up temp directory:', dirCleanupError);
        }
      }
    }

  } catch (error) {
    console.error('=== DOCUMENT PROCESSING ERROR ===');
    console.error('Error details:', error);
    throw error;
  }
};

export const extractDocumentData = async (fileOrPath: File | string, fileType?: string): Promise<ExtractedDocumentData> => {
  try {
    console.log('Starting document data extraction...');
    const text = await extractTextFromImage(fileOrPath, fileType);
    if (!text || text.trim().length === 0) {
      console.error('No text extracted from document');
      throw new Error('No text could be extracted from the document');
    }
    
    const parsedData = parsePassportData(text);
    
    // Check if we extracted meaningful data (at least name or passport number)
    if (!parsedData.name && !parsedData.passportNumber && !parsedData.iin) {
      console.warn('OCR extraction yielded insufficient data', { 
        extractedFields: Object.keys(parsedData).filter(k => k !== 'rawText'),
        textLength: text.length
      });
      
      // Return partial data instead of throwing error
      return { 
        ...parsedData,
        rawText: text 
      };
    }
    
    console.log('Document data extraction completed successfully');
    return parsedData;
  } catch (error) {
    console.error('Document data extraction error:', error);
    // Return an empty object with just the error message and raw text if possible
    return { 
      rawText: error instanceof Error ? error.message : 'Unknown error during OCR processing' 
    };
  }
}; 