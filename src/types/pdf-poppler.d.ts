declare module 'pdf-poppler' {
  interface ConvertOptions {
    format?: 'jpeg' | 'png' | 'tiff' | 'ps' | 'eps' | 'svg';
    out_dir?: string;
    out_prefix?: string;
    page?: number | null;
    quality?: number;
    scale?: number;
    single_file?: boolean;
  }

  interface ConvertResult {
    name: string;
    size: number;
    path: string;
  }

  function convert(file: string, options?: ConvertOptions): Promise<ConvertResult[]>;
  
  export = {
    convert
  };
}
